'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { Heart, ShoppingBag } from 'lucide-react';
import { formatPrice } from '@/lib/utils';

export default function ProductCard({ product }) {
  const [isHovered, setIsHovered] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleWishlistToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  const handleQuickAdd = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Add to cart logic here
    console.log('Quick add to cart:', product.id);
  };

  const images = product.images || [];
  const hasMultipleImages = images.length > 1;

  return (
    <div 
      className="group relative bg-white"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/products/${product.slug}`}>
        {/* Product Image */}
        <div className="relative aspect-[3/4] overflow-hidden bg-gray-100">
          {images.length > 0 ? (
            <>
              <Image
                src={images[currentImageIndex] || images[0]}
                alt={product.name}
                fill
                className={`object-cover transition-all duration-500 ${
                  isHovered && hasMultipleImages ? 'scale-105' : 'scale-100'
                }`}
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              />
              
              {/* Hover overlay with second image */}
              {hasMultipleImages && isHovered && (
                <Image
                  src={images[1]}
                  alt={product.name}
                  fill
                  className="object-cover transition-opacity duration-500 opacity-100"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                />
              )}
            </>
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-400 text-sm">No Image</span>
            </div>
          )}

          {/* Product badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {product.isNew && (
              <span className="bg-black text-white text-xs px-2 py-1 font-medium tracking-wide">
                NEW
              </span>
            )}
            {product.compareAtPrice && (
              <span className="bg-red-600 text-white text-xs px-2 py-1 font-medium tracking-wide">
                SALE
              </span>
            )}
          </div>

          {/* Wishlist button */}
          <button
            onClick={handleWishlistToggle}
            className={`absolute top-3 right-3 p-2 rounded-full transition-all duration-300 ${
              isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
            } ${
              isWishlisted 
                ? 'bg-black text-white' 
                : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white'
            }`}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
          </button>

          {/* Quick add button */}
          <button
            onClick={handleQuickAdd}
            className={`absolute bottom-3 left-3 right-3 bg-black text-white py-2 px-4 text-sm font-medium tracking-wide transition-all duration-300 ${
              isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
            } hover:bg-gray-800`}
          >
            QUICK ADD
          </button>

          {/* Color swatches */}
          {product.colors && product.colors.length > 0 && (
            <div className={`absolute bottom-3 right-3 flex gap-1 transition-all duration-300 ${
              isHovered ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
            }`}>
              {product.colors.slice(0, 3).map((color, index) => (
                <div
                  key={index}
                  className="w-3 h-3 rounded-full border border-white/50"
                  style={{ backgroundColor: color }}
                />
              ))}
              {product.colors.length > 3 && (
                <div className="w-3 h-3 rounded-full bg-gray-300 flex items-center justify-center">
                  <span className="text-xs text-gray-600">+</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="pt-4 pb-2">
          {/* Category */}
          {product.category && (
            <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">
              {product.category.name}
            </p>
          )}

          {/* Product Name */}
          <h3 className="text-sm font-light text-gray-900 mb-2 tracking-wide leading-tight">
            {product.name}
          </h3>

          {/* Price */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.compareAtPrice && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.compareAtPrice)}
              </span>
            )}
          </div>

          {/* Sizes */}
          {product.sizes && product.sizes.length > 0 && (
            <div className="mt-2 flex gap-1">
              {product.sizes.slice(0, 4).map((size, index) => (
                <span
                  key={index}
                  className="text-xs text-gray-600 border border-gray-200 px-1.5 py-0.5"
                >
                  {size}
                </span>
              ))}
              {product.sizes.length > 4 && (
                <span className="text-xs text-gray-500">
                  +{product.sizes.length - 4}
                </span>
              )}
            </div>
          )}
        </div>
      </Link>
    </div>
  );
}
