import { customAlphabet } from 'nanoid';

// Create a custom alphabet for IDs (URL-safe characters)
const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

// Generate different types of IDs with prefixes for better organization
const generateId = customAlphabet(alphabet, 21);

export function generateUserId() {
  return `user_${generateId()}`;
}

export function generateProductId() {
  return `prod_${generateId()}`;
}

export function generateCategoryId() {
  return `cat_${generateId()}`;
}

export function generateOrderId() {
  return `ord_${generateId()}`;
}

export function generateCartId() {
  return `cart_${generateId()}`;
}

export function generateWishlistId() {
  return `wish_${generateId()}`;
}

export function generateSessionId() {
  return `sess_${generateId()}`;
}

export function generateAccountId() {
  return `acc_${generateId()}`;
}

export function generateVerificationId() {
  return `ver_${generateId()}`;
}

// Generic ID generator
export function generateGenericId(prefix = '') {
  return prefix ? `${prefix}_${generateId()}` : generateId();
}

export default {
  generateUserId,
  generateProductId,
  generateCategoryId,
  generateOrderId,
  generateCartId,
  generateWishlistId,
  generateSessionId,
  generateAccountId,
  generateVerificationId,
  generateGenericId,
};
