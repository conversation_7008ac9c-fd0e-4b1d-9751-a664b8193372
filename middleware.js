import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

export async function middleware(request) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/products',
    '/categories',
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/verify-email',
    '/api/auth',
    '/api/products',
    '/api/categories',
  ];

  // Admin routes that require admin role
  const adminRoutes = [
    '/admin',
    '/api/admin',
    '/api/upload',
  ];

  // Protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/cart',
    '/checkout',
    '/api/cart',
    '/api/orders',
    '/api/wishlist',
  ];

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  // Check if the route is admin
  const isAdminRoute = adminRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  // Check if the route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    // Handle admin routes
    if (isAdminRoute) {
      if (!session?.user?.id) {
        return NextResponse.redirect(new URL('/auth/signin?redirect=' + pathname, request.url));
      }

      // Check if user is admin (this would need to be added to the session)
      // For now, we'll check the user's role from the database
      // This is handled in the admin-auth.js file for API routes
      
      return NextResponse.next();
    }

    // Handle protected routes
    if (isProtectedRoute) {
      if (!session?.user?.id) {
        return NextResponse.redirect(new URL('/auth/signin?redirect=' + pathname, request.url));
      }
      return NextResponse.next();
    }

    // Handle auth routes when user is already logged in
    if (pathname.startsWith('/auth/') && session?.user?.id) {
      // Redirect to dashboard if user is already authenticated
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // Allow public routes
    return NextResponse.next();

  } catch (error) {
    console.error('Middleware error:', error);
    
    // If there's an error and it's a protected route, redirect to signin
    if (isProtectedRoute || isAdminRoute) {
      return NextResponse.redirect(new URL('/auth/signin?redirect=' + pathname, request.url));
    }
    
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|images).*)',
  ],
};
