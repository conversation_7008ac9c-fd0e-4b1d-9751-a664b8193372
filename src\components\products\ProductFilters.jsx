'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, X, Filter } from 'lucide-react';

export default function ProductFilters({ 
  filters = {},
  onFiltersChange,
  categories = [],
  priceRanges = [
    { label: 'Under ₹2,000', min: 0, max: 2000 },
    { label: '₹2,000 - ₹5,000', min: 2000, max: 5000 },
    { label: '₹5,000 - ₹10,000', min: 5000, max: 10000 },
    { label: '₹10,000 - ₹20,000', min: 10000, max: 20000 },
    { label: 'Over ₹20,000', min: 20000, max: null },
  ],
  sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  colors = [
    { name: 'Black', value: '#000000' },
    { name: 'White', value: '#FFFFFF' },
    { name: 'Gray', value: '#808080' },
    { name: 'Navy', value: '#000080' },
    { name: '<PERSON>', value: '#8B4513' },
    { name: 'Beige', value: '#F5F5DC' },
  ],
  className = ""
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    price: true,
    size: false,
    color: false,
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterChange = (type, value, checked) => {
    const currentValues = filters[type] || [];
    let newValues;

    if (checked) {
      newValues = [...currentValues, value];
    } else {
      newValues = currentValues.filter(v => v !== value);
    }

    onFiltersChange({
      ...filters,
      [type]: newValues
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.values(filters).some(filterArray => 
    Array.isArray(filterArray) && filterArray.length > 0
  );

  const FilterSection = ({ title, type, children, count = 0 }) => (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        onClick={() => toggleSection(type)}
        className="w-full flex items-center justify-between py-4 text-left"
      >
        <span className="text-sm font-medium text-gray-900 tracking-wide">
          {title}
          {count > 0 && (
            <span className="ml-2 text-xs text-gray-500">({count})</span>
          )}
        </span>
        {expandedSections[type] ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>
      {expandedSections[type] && (
        <div className="pb-4">
          {children}
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-6">
        <button
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-900 hover:bg-gray-50 transition-colors"
        >
          <Filter className="h-4 w-4" />
          Filters
          {hasActiveFilters && (
            <span className="bg-black text-white text-xs px-1.5 py-0.5 rounded-full">
              {Object.values(filters).reduce((acc, arr) => acc + (arr?.length || 0), 0)}
            </span>
          )}
        </button>
      </div>

      {/* Desktop Filters */}
      <div className={`hidden lg:block ${className}`}>
        <div className="bg-white">
          {/* Header */}
          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 tracking-wide">FILTERS</h3>
            {hasActiveFilters && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-gray-600 hover:text-gray-900 underline"
              >
                Clear All
              </button>
            )}
          </div>

          {/* Filter Sections */}
          <div className="mt-4">
            {/* Categories */}
            <FilterSection 
              title="CATEGORY" 
              type="category"
              count={filters.category?.length || 0}
            >
              <div className="space-y-3">
                {categories.map((category) => (
                  <label key={category.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.category?.includes(category.slug) || false}
                      onChange={(e) => handleFilterChange('category', category.slug, e.target.checked)}
                      className="h-4 w-4 text-black border-gray-300 rounded focus:ring-black"
                    />
                    <span className="ml-3 text-sm text-gray-700">{category.name}</span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Price Range */}
            <FilterSection 
              title="PRICE" 
              type="price"
              count={filters.price?.length || 0}
            >
              <div className="space-y-3">
                {priceRanges.map((range, index) => (
                  <label key={index} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.price?.includes(`${range.min}-${range.max}`) || false}
                      onChange={(e) => handleFilterChange('price', `${range.min}-${range.max}`, e.target.checked)}
                      className="h-4 w-4 text-black border-gray-300 rounded focus:ring-black"
                    />
                    <span className="ml-3 text-sm text-gray-700">{range.label}</span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Sizes */}
            <FilterSection 
              title="SIZE" 
              type="size"
              count={filters.size?.length || 0}
            >
              <div className="grid grid-cols-3 gap-2">
                {sizes.map((size) => (
                  <label key={size} className="flex items-center justify-center">
                    <input
                      type="checkbox"
                      checked={filters.size?.includes(size) || false}
                      onChange={(e) => handleFilterChange('size', size, e.target.checked)}
                      className="sr-only"
                    />
                    <span className={`w-full py-2 text-center text-sm border transition-colors cursor-pointer ${
                      filters.size?.includes(size)
                        ? 'border-black bg-black text-white'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}>
                      {size}
                    </span>
                  </label>
                ))}
              </div>
            </FilterSection>

            {/* Colors */}
            <FilterSection 
              title="COLOR" 
              type="color"
              count={filters.color?.length || 0}
            >
              <div className="grid grid-cols-4 gap-3">
                {colors.map((color) => (
                  <label key={color.value} className="flex flex-col items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.color?.includes(color.value) || false}
                      onChange={(e) => handleFilterChange('color', color.value, e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-8 h-8 rounded-full border-2 transition-all ${
                      filters.color?.includes(color.value)
                        ? 'border-black scale-110'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}>
                      <div 
                        className="w-full h-full rounded-full"
                        style={{ backgroundColor: color.value }}
                      />
                    </div>
                    <span className="text-xs text-gray-600 mt-1">{color.name}</span>
                  </label>
                ))}
              </div>
            </FilterSection>
          </div>
        </div>
      </div>

      {/* Mobile Filter Modal */}
      {isOpen && (
        <div className="lg:hidden fixed inset-0 z-50 bg-white">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Filters</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Filter Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {/* Same filter sections as desktop but in mobile layout */}
              <div className="space-y-6">
                {/* Categories */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">CATEGORY</h4>
                  <div className="space-y-3">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.category?.includes(category.slug) || false}
                          onChange={(e) => handleFilterChange('category', category.slug, e.target.checked)}
                          className="h-4 w-4 text-black border-gray-300 rounded focus:ring-black"
                        />
                        <span className="ml-3 text-sm text-gray-700">{category.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">PRICE</h4>
                  <div className="space-y-3">
                    {priceRanges.map((range, index) => (
                      <label key={index} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.price?.includes(`${range.min}-${range.max}`) || false}
                          onChange={(e) => handleFilterChange('price', `${range.min}-${range.max}`, e.target.checked)}
                          className="h-4 w-4 text-black border-gray-300 rounded focus:ring-black"
                        />
                        <span className="ml-3 text-sm text-gray-700">{range.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Sizes */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">SIZE</h4>
                  <div className="grid grid-cols-3 gap-2">
                    {sizes.map((size) => (
                      <label key={size} className="flex items-center justify-center">
                        <input
                          type="checkbox"
                          checked={filters.size?.includes(size) || false}
                          onChange={(e) => handleFilterChange('size', size, e.target.checked)}
                          className="sr-only"
                        />
                        <span className={`w-full py-2 text-center text-sm border transition-colors cursor-pointer ${
                          filters.size?.includes(size)
                            ? 'border-black bg-black text-white'
                            : 'border-gray-300 text-gray-700 hover:border-gray-400'
                        }`}>
                          {size}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Colors */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">COLOR</h4>
                  <div className="grid grid-cols-4 gap-3">
                    {colors.map((color) => (
                      <label key={color.value} className="flex flex-col items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.color?.includes(color.value) || false}
                          onChange={(e) => handleFilterChange('color', color.value, e.target.checked)}
                          className="sr-only"
                        />
                        <div className={`w-8 h-8 rounded-full border-2 transition-all ${
                          filters.color?.includes(color.value)
                            ? 'border-black scale-110'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}>
                          <div 
                            className="w-full h-full rounded-full"
                            style={{ backgroundColor: color.value }}
                          />
                        </div>
                        <span className="text-xs text-gray-600 mt-1">{color.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 space-y-3">
              {hasActiveFilters && (
                <button
                  onClick={clearAllFilters}
                  className="w-full py-2 text-sm text-gray-600 hover:text-gray-900 underline"
                >
                  Clear All Filters
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="w-full bg-black text-white py-3 text-sm font-medium tracking-wide hover:bg-gray-800 transition-colors"
              >
                APPLY FILTERS
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
