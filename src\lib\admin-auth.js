import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/db';
import { users } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { useSession } from '@/lib/auth-client';

export async function checkAdminAuth(request) {
  try {
    // Get session from the auth library
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return {
        isAdmin: false,
        user: null,
        error: 'Not authenticated'
      };
    }

    // Get user from database to check role
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        role: users.role,
      })
      .from(users)
      .where(eq(users.id, session.user.id));

    if (!user) {
      return {
        isAdmin: false,
        user: null,
        error: 'User not found'
      };
    }

    const isAdmin = user.role === 'admin';

    return {
      isAdmin,
      user,
      error: isAdmin ? null : 'Admin access required'
    };
  } catch (error) {
    console.error('Admin auth check error:', error);
    return {
      isAdmin: false,
      user: null,
      error: 'Authentication error'
    };
  }
}

export function withAdminAuth(handler) {
  return async (request, context) => {
    const authResult = await checkAdminAuth(request);
    
    if (!authResult.isAdmin) {
      return NextResponse.json(
        { error: authResult.error || 'Admin access required' },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      );
    }

    // Add user to request context
    request.user = authResult.user;
    
    return handler(request, context);
  };
}

// Client-side hook for checking admin status
export function useAdminAuth() {
  const { data: session, isLoading } = useSession();
  
  return {
    isAdmin: session?.user?.role === 'admin',
    isLoading,
    user: session?.user
  };
}
