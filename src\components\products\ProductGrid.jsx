'use client';

import { useState } from 'react';
import ProductCard from './ProductCard';
import { ChevronDown, Grid3X3, Grid2X2 } from 'lucide-react';

export default function ProductGrid({ 
  products = [], 
  isLoading = false,
  hasMore = false,
  onLoadMore,
  className = ""
}) {
  const [viewMode, setViewMode] = useState('grid-4'); // grid-2, grid-3, grid-4

  const gridClasses = {
    'grid-2': 'grid-cols-2 gap-4 md:gap-6',
    'grid-3': 'grid-cols-2 md:grid-cols-3 gap-4 md:gap-6',
    'grid-4': 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6'
  };

  if (isLoading && products.length === 0) {
    return (
      <div className={`${className}`}>
        {/* Loading skeleton */}
        <div className={`grid ${gridClasses[viewMode]}`}>
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="aspect-[3/4] bg-gray-200 rounded-sm mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!isLoading && products.length === 0) {
    return (
      <div className={`${className} flex flex-col items-center justify-center py-16`}>
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* View Mode Toggle - Hidden on mobile */}
      <div className="hidden md:flex justify-end mb-6">
        <div className="flex items-center gap-2 border border-gray-200 rounded-sm p-1">
          <button
            onClick={() => setViewMode('grid-2')}
            className={`p-1.5 rounded-sm transition-colors ${
              viewMode === 'grid-2' 
                ? 'bg-black text-white' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Grid2X2 className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('grid-3')}
            className={`p-1.5 rounded-sm transition-colors ${
              viewMode === 'grid-3' 
                ? 'bg-black text-white' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Grid3X3 className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('grid-4')}
            className={`p-1.5 rounded-sm transition-colors ${
              viewMode === 'grid-4' 
                ? 'bg-black text-white' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <div className="grid grid-cols-2 gap-0.5 w-4 h-4">
              <div className="bg-current rounded-[1px]"></div>
              <div className="bg-current rounded-[1px]"></div>
              <div className="bg-current rounded-[1px]"></div>
              <div className="bg-current rounded-[1px]"></div>
            </div>
          </button>
        </div>
      </div>

      {/* Products Grid */}
      <div className={`grid ${gridClasses[viewMode]}`}>
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center mt-12">
          <button
            onClick={onLoadMore}
            disabled={isLoading}
            className="flex items-center gap-2 px-8 py-3 border border-gray-300 text-gray-900 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-900 rounded-full animate-spin"></div>
                Loading...
              </>
            ) : (
              <>
                Load More
                <ChevronDown className="h-4 w-4" />
              </>
            )}
          </button>
        </div>
      )}

      {/* Loading more products */}
      {isLoading && products.length > 0 && (
        <div className={`grid ${gridClasses[viewMode]} mt-6`}>
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={`loading-${index}`} className="animate-pulse">
              <div className="aspect-[3/4] bg-gray-200 rounded-sm mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
