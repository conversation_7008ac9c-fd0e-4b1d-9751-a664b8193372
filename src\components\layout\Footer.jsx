import Link from 'next/link';
import { Instagram, Facebook, Twitter, Youtube } from 'lucide-react';

export default function Footer() {
  const footerSections = [
    {
      title: 'HELP',
      links: [
        { name: 'Customer Service', href: '/customer-service' },
        { name: 'Size Guide', href: '/size-guide' },
        { name: 'Care Guide', href: '/care-guide' },
        { name: 'Shipping & Returns', href: '/shipping-returns' },
        { name: 'Exchange & Returns', href: '/exchange-returns' },
        { name: 'Payment Methods', href: '/payment-methods' },
        { name: 'Gift Card', href: '/gift-card' },
        { name: 'FAQ', href: '/faq' },
      ],
    },
    {
      title: 'FOLLOW US',
      links: [
        { name: 'Newsletter', href: '/newsletter' },
        { name: 'Instagram', href: 'https://instagram.com/obsess' },
        { name: 'Facebook', href: 'https://facebook.com/obsess' },
        { name: 'Twitter', href: 'https://twitter.com/obsess' },
        { name: 'Youtube', href: 'https://youtube.com/obsess' },
      ],
    },
    {
      title: 'COMPANY',
      links: [
        { name: 'About Us', href: '/about' },
        { name: 'Join Life', href: '/sustainability' },
        { name: 'Careers', href: '/careers' },
        { name: 'Press', href: '/press' },
        { name: 'Investor Relations', href: '/investors' },
      ],
    },
    {
      title: 'POLICIES',
      links: [
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Use', href: '/terms' },
        { name: 'Cookies Settings', href: '/cookies' },
        { name: 'Accessibility', href: '/accessibility' },
      ],
    },
  ];

  return (
    <footer className="bg-white border-t border-neutral-200 text-neutral-800">
      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Minimalist Footer Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 border-b border-neutral-200 pb-10">
          {footerSections.map((section) => (
            <div key={section.title}>
              <h4 className="text-xs font-medium tracking-widest uppercase mb-4 text-neutral-500">
                {section.title}
              </h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-xs font-light tracking-widest hover:text-black transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Brand & Social - Minimalist Row */}
        <div className="flex flex-col md:flex-row items-center justify-between py-8 border-b border-neutral-200">
          <Link href="/" className="text-xl font-light tracking-widest mb-4 md:mb-0">
            OBSESS
          </Link>
          <div className="flex space-x-5">
            <a href="https://instagram.com/obsess" className="hover:text-black transition-colors" aria-label="Instagram">
              <Instagram className="h-5 w-5" />
            </a>
            <a href="https://facebook.com/obsess" className="hover:text-black transition-colors" aria-label="Facebook">
              <Facebook className="h-5 w-5" />
            </a>
            <a href="https://twitter.com/obsess" className="hover:text-black transition-colors" aria-label="Twitter">
              <Twitter className="h-5 w-5" />
            </a>
            <a href="https://youtube.com/obsess" className="hover:text-black transition-colors" aria-label="Youtube">
              <Youtube className="h-5 w-5" />
            </a>
          </div>
        </div>

        {/* Bottom Bar - Minimalist */}
        <div className="flex flex-col md:flex-row items-center justify-between text-xs text-neutral-400 pt-8 gap-2">
          <div className="flex flex-wrap gap-x-6 gap-y-2 items-center">
            <span>© 2024 OBSESS</span>
            <Link href="/privacy" className="hover:text-black transition-colors">Privacy Policy</Link>
            <Link href="/terms" className="hover:text-black transition-colors">Terms of Use</Link>
            <Link href="/cookies" className="hover:text-black transition-colors">Cookies Settings</Link>
          </div>
          <div className="flex items-center gap-2">
            <span>INDIA</span>
            <span>|</span>
            <span>ENGLISH</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
