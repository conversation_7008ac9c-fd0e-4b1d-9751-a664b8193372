import "./globals.css";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AuthProvider from "@/components/auth/AuthProvider";

export const metadata = {
  title: "OBSESS - Premium Fashion Store",
  description: "Discover premium fashion for the modern individual. Shop the latest trends in clothing, accessories, and more at OBSESS.",
  keywords: "fashion, clothing, premium, style, obsess, ecommerce",
  openGraph: {
    title: "OBSESS - Premium Fashion Store",
    description: "Discover premium fashion for the modern individual.",
    url: "https://obsess.shop",
    siteName: "OBSESS",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "OBSESS Fashion Store",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "OBSESS - Premium Fashion Store",
    description: "Discover premium fashion for the modern individual.",
    images: ["/images/og-image.jpg"],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className="antialiased">
        <AuthProvider>
          <div className="min-h-screen flex flex-col">
            <Navbar />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
