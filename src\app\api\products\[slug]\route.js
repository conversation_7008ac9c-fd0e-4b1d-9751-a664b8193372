import { NextResponse } from 'next/server';
import { db } from '@/db';
import { products, categories } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request, { params }) {
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { error: 'Product slug is required' },
        { status: 400 }
      );
    }

    // Fetch product with category information
    const [product] = await db
      .select({
        id: products.id,
        name: products.name,
        slug: products.slug,
        description: products.description,
        price: products.price,
        compareAtPrice: products.compareAtPrice,
        images: products.images,
        sizes: products.sizes,
        colors: products.colors,
        inventory: products.inventory,
        featured: products.featured,
        active: products.active,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
        category: {
          id: categories.id,
          name: categories.name,
          slug: categories.slug,
        },
      })
      .from(products)
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .where(and(
        eq(products.slug, slug),
        eq(products.active, true)
      ));

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Convert price fields to numbers for easier handling
    const formattedProduct = {
      ...product,
      price: parseFloat(product.price),
      compareAtPrice: product.compareAtPrice ? parseFloat(product.compareAtPrice) : null,
    };

    return NextResponse.json(formattedProduct);
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

export async function PUT(request, { params }) {
  try {
    const { slug } = params;
    const body = await request.json();

    if (!slug) {
      return NextResponse.json(
        { error: 'Product slug is required' },
        { status: 400 }
      );
    }

    // Check if product exists
    const [existingProduct] = await db
      .select({ id: products.id })
      .from(products)
      .where(eq(products.slug, slug));

    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const {
      name,
      description,
      price,
      compareAtPrice,
      categoryId,
      images,
      sizes,
      colors,
      inventory,
      featured,
      active,
    } = body;

    // Update product
    const [updatedProduct] = await db
      .update(products)
      .set({
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(price && { price: price.toString() }),
        ...(compareAtPrice !== undefined && { 
          compareAtPrice: compareAtPrice ? compareAtPrice.toString() : null 
        }),
        ...(categoryId && { categoryId }),
        ...(images && { images }),
        ...(sizes && { sizes }),
        ...(colors && { colors }),
        ...(inventory !== undefined && { inventory }),
        ...(featured !== undefined && { featured }),
        ...(active !== undefined && { active }),
        updatedAt: new Date(),
      })
      .where(eq(products.slug, slug))
      .returning();

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { error: 'Product slug is required' },
        { status: 400 }
      );
    }

    // Check if product exists
    const [existingProduct] = await db
      .select({ id: products.id })
      .from(products)
      .where(eq(products.slug, slug));

    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting active to false
    const [deletedProduct] = await db
      .update(products)
      .set({
        active: false,
        updatedAt: new Date(),
      })
      .where(eq(products.slug, slug))
      .returning();

    return NextResponse.json({ 
      message: 'Product deleted successfully',
      product: deletedProduct 
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
