'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Heart, ShoppingBag, Truck, RefreshCw, Shield, ChevronRight, Star } from 'lucide-react';
import ProductImageGallery from '@/components/products/ProductImageGallery';
import Button from '@/components/ui/Button';
import { formatPrice } from '@/lib/utils';

export default function ProductDetailsPage() {
  const params = useParams();
  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  useEffect(() => {
    if (params.slug) {
      fetchProduct(params.slug);
    }
  }, [params.slug]);

  const fetchProduct = async (slug) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/products/${slug}`);
      
      if (response.ok) {
        const data = await response.json();
        setProduct(data);
        
        // Set default selections
        if (data.sizes?.length > 0) {
          setSelectedSize(data.sizes[0]);
        }
        if (data.colors?.length > 0) {
          setSelectedColor(data.colors[0]);
        }
      } else if (response.status === 404) {
        setError('Product not found');
      } else {
        setError('Failed to load product');
      }
    } catch (err) {
      setError('Failed to load product');
      console.error('Error fetching product:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCart = async () => {
    if (!selectedSize && product.sizes?.length > 0) {
      alert('Please select a size');
      return;
    }
    
    if (!selectedColor && product.colors?.length > 0) {
      alert('Please select a color');
      return;
    }

    setIsAddingToCart(true);
    
    try {
      // Add to cart logic here
      console.log('Adding to cart:', {
        productId: product.id,
        size: selectedSize,
        color: selectedColor,
        quantity
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message or redirect to cart
      alert('Product added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add product to cart');
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
    // Add wishlist logic here
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Image skeleton */}
            <div className="space-y-4">
              <div className="aspect-[3/4] bg-gray-200 animate-pulse"></div>
              <div className="flex gap-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="w-16 aspect-[3/4] bg-gray-200 animate-pulse"></div>
                ))}
              </div>
            </div>
            
            {/* Content skeleton */}
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 animate-pulse w-1/4"></div>
                <div className="h-8 bg-gray-200 animate-pulse w-3/4"></div>
                <div className="h-6 bg-gray-200 animate-pulse w-1/3"></div>
              </div>
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Product Not Found</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link href="/products">
            <Button>Browse Products</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!product) {
    return null;
  }

  const discountPercentage = product.compareAtPrice 
    ? Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-gray-900">Home</Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/products" className="hover:text-gray-900">Products</Link>
            {product.category && (
              <>
                <ChevronRight className="h-4 w-4" />
                <Link 
                  href={`/products?category=${product.category.slug}`}
                  className="hover:text-gray-900"
                >
                  {product.category.name}
                </Link>
              </>
            )}
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900">{product.name}</span>
          </nav>
        </div>
      </div>

      {/* Product Details */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery 
              images={product.images || []}
              productName={product.name}
            />
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              {product.category && (
                <p className="text-sm text-gray-500 uppercase tracking-wide mb-2">
                  {product.category.name}
                </p>
              )}
              <h1 className="text-2xl lg:text-3xl font-light text-gray-900 tracking-wide leading-tight">
                {product.name}
              </h1>
            </div>

            {/* Price */}
            <div className="flex items-center gap-3">
              <span className="text-2xl font-medium text-gray-900">
                {formatPrice(product.price)}
              </span>
              {product.compareAtPrice && (
                <>
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(product.compareAtPrice)}
                  </span>
                  <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                    {discountPercentage}% OFF
                  </span>
                </>
              )}
            </div>

            {/* Description */}
            {product.description && (
              <div className="prose prose-sm text-gray-700">
                <p>{product.description}</p>
              </div>
            )}

            {/* Color Selection */}
            {product.colors && product.colors.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  COLOR: {selectedColor && (
                    <span className="font-normal text-gray-600 ml-1">
                      {typeof selectedColor === 'object' ? selectedColor.name : selectedColor}
                    </span>
                  )}
                </h3>
                <div className="flex gap-3">
                  {product.colors.map((color, index) => {
                    const colorValue = typeof color === 'object' ? color.value : color;
                    const colorName = typeof color === 'object' ? color.name : color;
                    
                    return (
                      <button
                        key={index}
                        onClick={() => setSelectedColor(color)}
                        className={`w-10 h-10 rounded-full border-2 transition-all ${
                          selectedColor === color
                            ? 'border-black scale-110'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        title={colorName}
                      >
                        <div 
                          className="w-full h-full rounded-full"
                          style={{ backgroundColor: colorValue }}
                        />
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Size Selection */}
            {product.sizes && product.sizes.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  SIZE: {selectedSize && (
                    <span className="font-normal text-gray-600 ml-1">{selectedSize}</span>
                  )}
                </h3>
                <div className="grid grid-cols-4 gap-2">
                  {product.sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`py-3 text-center text-sm border transition-colors ${
                        selectedSize === size
                          ? 'border-black bg-black text-white'
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">QUANTITY</h3>
              <div className="flex items-center border border-gray-300 w-fit">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                  {quantity}
                </span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  +
                </button>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={handleAddToCart}
                  disabled={isAddingToCart || product.inventory === 0}
                  className="flex-1 h-12"
                >
                  {isAddingToCart ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Adding...
                    </div>
                  ) : product.inventory === 0 ? (
                    'Out of Stock'
                  ) : (
                    <>
                      <ShoppingBag className="h-4 w-4 mr-2" />
                      Add to Cart
                    </>
                  )}
                </Button>
                
                <button
                  onClick={handleWishlistToggle}
                  className={`p-3 border transition-colors ${
                    isWishlisted
                      ? 'border-black bg-black text-white'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
                </button>
              </div>

              {/* Stock status */}
              {product.inventory > 0 && product.inventory <= 10 && (
                <p className="text-sm text-orange-600">
                  Only {product.inventory} left in stock
                </p>
              )}
            </div>

            {/* Features */}
            <div className="border-t border-gray-200 pt-6 space-y-4">
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <Truck className="h-5 w-5" />
                <span>Free shipping on orders over ₹2,999</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <RefreshCw className="h-5 w-5" />
                <span>Easy 30-day returns</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <Shield className="h-5 w-5" />
                <span>2-year warranty</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
