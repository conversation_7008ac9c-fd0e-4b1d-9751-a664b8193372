import { createAuthClient } from 'better-auth/react';

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  fetchOptions: {
    onError: (e) => {
      if (e.status === 401) {
        // Handle unauthorized errors
        console.error('Authentication error:', e);
      }
    },
  },
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  forgetPassword,
  resetPassword,
  verifyEmail,
  changePassword,
  updateUser,
} = authClient;
