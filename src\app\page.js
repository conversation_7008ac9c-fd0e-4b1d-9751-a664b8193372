import Image from "next/image";
import Link from "next/link";
import { ArrowR<PERSON>, Star, Truck, Shield, RefreshCw } from "lucide-react";

export default function Home() {
  return (
    <main className="bg-white min-h-screen flex flex-col items-center w-full">
      {/* Minimalist Hero Section */}
      <section className="w-full relative h-[70vh] flex items-center justify-center overflow-hidden border-b border-neutral-200">
        <Image
          src="/images/hero.jpg"
          alt="Hero"
          fill
          className="object-cover object-center w-full h-full"
          priority
        />
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/10 px-4 text-center">
          <h1 className="text-white text-5xl md:text-7xl font-light tracking-tight uppercase mb-6" style={{letterSpacing: '0.1em'}}>Minimal Collection</h1>
          <Link href="/products" className="inline-block border border-white text-white px-10 py-3 text-base font-light uppercase tracking-widest hover:bg-white hover:text-black transition-colors duration-200">Enter</Link>
        </div>
      </section>

      {/* Minimal Category Grid */}
      <section className="w-full max-w-6xl px-2 sm:px-4 py-12 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link href="/products?category=women" className="relative group h-64 md:h-96 overflow-hidden">
          <Image src="/images/women.jpg" alt="Women" fill className="object-cover group-hover:scale-105 transition-transform duration-700" />
          <span className="absolute inset-0 flex items-center justify-center bg-black/10 group-hover:bg-black/20 transition">
            <span className="text-white text-2xl md:text-4xl font-light uppercase tracking-widest">Women</span>
          </span>
        </Link>
        <Link href="/products?category=men" className="relative group h-64 md:h-96 overflow-hidden">
          <Image src="/images/men.jpg" alt="Men" fill className="object-cover group-hover:scale-105 transition-transform duration-700" />
          <span className="absolute inset-0 flex items-center justify-center bg-black/10 group-hover:bg-black/20 transition">
            <span className="text-white text-2xl md:text-4xl font-light uppercase tracking-widest">Men</span>
          </span>
        </Link>
        <Link href="/products?category=accessories" className="relative group h-64 md:h-96 overflow-hidden">
          <Image src="/images/accessories.jpg" alt="Accessories" fill className="object-cover group-hover:scale-105 transition-transform duration-700" />
          <span className="absolute inset-0 flex items-center justify-center bg-black/10 group-hover:bg-black/20 transition">
            <span className="text-white text-2xl md:text-4xl font-light uppercase tracking-widest">Accessories</span>
          </span>
        </Link>
      </section>
    </main>
  );
}