import { NextResponse } from 'next/server';
import { db } from '@/db';
import { products, categories } from '@/db/schema';
import { eq, like, and, or, desc, asc } from 'drizzle-orm';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'featured';
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = parseInt(searchParams.get('offset')) || 0;

    let query = db.select({
      id: products.id,
      name: products.name,
      slug: products.slug,
      description: products.description,
      price: products.price,
      compareAtPrice: products.compareAtPrice,
      images: products.images,
      sizes: products.sizes,
      colors: products.colors,
      inventory: products.inventory,
      featured: products.featured,
      active: products.active,
      createdAt: products.createdAt,
      category: {
        id: categories.id,
        name: categories.name,
        slug: categories.slug,
      },
    })
    .from(products)
    .leftJoin(categories, eq(products.categoryId, categories.id))
    .where(eq(products.active, true));

    // Apply filters
    const conditions = [eq(products.active, true)];

    if (category && category !== 'all') {
      conditions.push(eq(categories.slug, category));
    }

    if (search) {
      conditions.push(
        or(
          like(products.name, `%${search}%`),
          like(products.description, `%${search}%`)
        )
      );
    }

    if (conditions.length > 1) {
      query = query.where(and(...conditions));
    }

    // Apply sorting
    switch (sort) {
      case 'price-low':
        query = query.orderBy(asc(products.price));
        break;
      case 'price-high':
        query = query.orderBy(desc(products.price));
        break;
      case 'newest':
        query = query.orderBy(desc(products.createdAt));
        break;
      case 'name':
        query = query.orderBy(asc(products.name));
        break;
      default:
        // Featured first, then by creation date
        query = query.orderBy(desc(products.featured), desc(products.createdAt));
    }

    // Apply pagination
    query = query.limit(limit).offset(offset);

    const result = await query;

    return NextResponse.json({
      products: result,
      pagination: {
        limit,
        offset,
        total: result.length, // In a real app, you'd get the total count separately
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      price,
      compareAtPrice,
      categoryId,
      images,
      sizes,
      colors,
      inventory,
      featured = false,
    } = body;

    // Validate required fields
    if (!name || !price || !categoryId) {
      return NextResponse.json(
        { error: 'Name, price, and category are required' },
        { status: 400 }
      );
    }

    // Generate slug from name
    const slug = name
      .toLowerCase()
      .replace(/[^\w ]+/g, '')
      .replace(/ +/g, '-');

    const [newProduct] = await db.insert(products).values({
      name,
      slug,
      description,
      price: price.toString(),
      compareAtPrice: compareAtPrice ? compareAtPrice.toString() : null,
      categoryId,
      images: images || [],
      sizes: sizes || [],
      colors: colors || [],
      inventory: inventory || 0,
      featured,
      active: true,
    }).returning();

    return NextResponse.json(newProduct, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
