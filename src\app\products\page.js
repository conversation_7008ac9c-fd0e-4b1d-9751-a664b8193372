'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import ProductGrid from '@/components/products/ProductGrid';
import ProductFilters from '@/components/products/ProductFilters';
import { ChevronDown, Search, X } from 'lucide-react';

export default function ProductsPage() {
  const searchParams = useSearchParams();
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  
  // Filter and search states
  const [filters, setFilters] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('featured');
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const sortOptions = [
    { value: 'featured', label: 'Featured' },
    { value: 'newest', label: 'Newest' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'name', label: 'Name A-Z' },
  ];

  // Initialize filters from URL params
  useEffect(() => {
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort');

    if (category) {
      setFilters(prev => ({ ...prev, category: [category] }));
    }
    if (search) {
      setSearchQuery(search);
    }
    if (sort) {
      setSortBy(sort);
    }
  }, [searchParams]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch products
  useEffect(() => {
    fetchProducts(true);
  }, [filters, searchQuery, sortBy]);

  const fetchProducts = async (reset = false) => {
    setIsLoading(true);
    
    try {
      const params = new URLSearchParams();
      
      // Add filters
      if (filters.category?.length) {
        params.append('category', filters.category[0]); // For simplicity, use first category
      }
      if (searchQuery) {
        params.append('search', searchQuery);
      }
      if (sortBy) {
        params.append('sort', sortBy);
      }
      
      // Pagination
      const page = reset ? 1 : currentPage;
      params.append('limit', '20');
      params.append('offset', ((page - 1) * 20).toString());

      const response = await fetch(`/api/products?${params.toString()}`);
      
      if (response.ok) {
        const data = await response.json();
        
        if (reset) {
          setProducts(data.products || []);
          setCurrentPage(1);
        } else {
          setProducts(prev => [...prev, ...(data.products || [])]);
        }
        
        setTotalProducts(data.total || 0);
        setHasMore((data.products?.length || 0) === 20);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
    fetchProducts(false);
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setIsSearchOpen(false);
    // fetchProducts will be triggered by useEffect
  };

  const clearSearch = () => {
    setSearchQuery('');
    setIsSearchOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6 lg:py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              {/* Title and count */}
              <div>
                <h1 className="text-2xl lg:text-3xl font-light text-gray-900 tracking-wide">
                  {filters.category?.length ? 
                    categories.find(cat => cat.slug === filters.category[0])?.name?.toUpperCase() || 'PRODUCTS'
                    : 'ALL PRODUCTS'
                  }
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  {totalProducts} {totalProducts === 1 ? 'product' : 'products'}
                </p>
              </div>

              {/* Search and Sort */}
              <div className="flex items-center gap-4">
                {/* Search */}
                <div className="relative">
                  {isSearchOpen ? (
                    <form onSubmit={handleSearchSubmit} className="flex items-center">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Search products..."
                        className="w-64 px-4 py-2 border border-gray-300 focus:outline-none focus:border-black text-sm"
                        autoFocus
                      />
                      <button
                        type="button"
                        onClick={clearSearch}
                        className="ml-2 p-2 text-gray-500 hover:text-gray-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </form>
                  ) : (
                    <button
                      onClick={() => setIsSearchOpen(true)}
                      className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 hover:border-gray-400 transition-colors"
                    >
                      <Search className="h-4 w-4" />
                      <span className="text-sm">Search</span>
                    </button>
                  )}
                </div>

                {/* Sort */}
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm text-gray-700 hover:border-gray-400 focus:outline-none focus:border-black cursor-pointer"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <ProductFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              categories={categories}
            />
          </div>

          {/* Products Grid */}
          <div className="lg:col-span-3">
            <ProductGrid
              products={products}
              isLoading={isLoading}
              hasMore={hasMore}
              onLoadMore={handleLoadMore}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
