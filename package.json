{"name": "obsess", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@types/node": "^24.0.7", "better-auth": "^1.2.12", "clsx": "^2.1.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "imagekit-javascript": "^4.0.1", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-seo": "^6.8.0", "postgres": "^3.4.7", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4"}}