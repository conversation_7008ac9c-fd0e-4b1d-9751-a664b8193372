'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, XCircle, Mail } from 'lucide-react';
import Button from '@/components/ui/Button';
import { verifyEmail } from '@/lib/auth-client';

export default function VerifyEmailPage() {
  const [status, setStatus] = useState('verifying'); // verifying, success, error
  const [error, setError] = useState('');
  const [isResending, setIsResending] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setStatus('error');
      setError('Invalid or missing verification token');
      return;
    }

    const verify = async () => {
      try {
        await verifyEmail({
          token,
        });
        setStatus('success');
      } catch (err) {
        setStatus('error');
        setError(err.message || 'Failed to verify email');
      }
    };

    verify();
  }, [searchParams]);

  const handleResendVerification = async () => {
    setIsResending(true);
    try {
      // This would need to be implemented in the auth client
      // For now, we'll show a message
      alert('Please contact support to resend verification email');
    } catch (err) {
      setError(err.message || 'Failed to resend verification email');
    } finally {
      setIsResending(false);
    }
  };

  const handleContinue = () => {
    router.push('/auth/signin');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex justify-center">
          <h1 className="text-3xl font-bold text-black">OBSESS</h1>
        </Link>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {status === 'verifying' && (
              <>
                <Mail className="mx-auto h-12 w-12 text-gray-400 animate-pulse" />
                <h2 className="mt-4 text-2xl font-bold text-gray-900">
                  Verifying your email...
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  Please wait while we verify your email address.
                </p>
              </>
            )}

            {status === 'success' && (
              <>
                <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                <h2 className="mt-4 text-2xl font-bold text-gray-900">
                  Email verified successfully!
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  Your email has been verified. You can now sign in to your account.
                </p>
                <div className="mt-6">
                  <Button onClick={handleContinue} className="w-full">
                    Continue to sign in
                  </Button>
                </div>
              </>
            )}

            {status === 'error' && (
              <>
                <XCircle className="mx-auto h-12 w-12 text-red-500" />
                <h2 className="mt-4 text-2xl font-bold text-gray-900">
                  Verification failed
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  {error || 'We couldn\'t verify your email address.'}
                </p>
                <div className="mt-6 space-y-3">
                  <Button
                    onClick={handleResendVerification}
                    disabled={isResending}
                    variant="outline"
                    className="w-full"
                  >
                    {isResending ? 'Resending...' : 'Resend verification email'}
                  </Button>
                  <Link
                    href="/auth/signin"
                    className="block w-full text-center text-sm text-gray-600 hover:text-gray-900"
                  >
                    Back to sign in
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
