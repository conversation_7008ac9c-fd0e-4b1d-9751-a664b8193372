'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import { signIn, signUp, signOut } from '@/lib/auth-client';
import { useState } from 'react';

export default function TestAuthPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [testData, setTestData] = useState({
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Test User',
  });

  const handleSignUp = async () => {
    try {
      await signUp.email({
        email: testData.email,
        password: testData.password,
        name: testData.name,
      });
      alert('Sign up successful!');
    } catch (error) {
      alert('Sign up failed: ' + error.message);
    }
  };

  const handleSignIn = async () => {
    try {
      await signIn.email({
        email: testData.email,
        password: testData.password,
      });
      alert('Sign in successful!');
    } catch (error) {
      alert('Sign in failed: ' + error.message);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      alert('Sign out successful!');
    } catch (error) {
      alert('Sign out failed: ' + error.message);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Auth System Test</h1>
        
        {/* Auth Status */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            {user && (
              <>
                <p><strong>User ID:</strong> {user.id}</p>
                <p><strong>Name:</strong> {user.name}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Role:</strong> {user.role}</p>
                <p><strong>Email Verified:</strong> {user.emailVerified ? 'Yes' : 'No'}</p>
              </>
            )}
          </div>
        </div>

        {/* Test Form */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Credentials</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                value={testData.name}
                onChange={(e) => setTestData({ ...testData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                value={testData.email}
                onChange={(e) => setTestData({ ...testData, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <input
                type="password"
                value={testData.password}
                onChange={(e) => setTestData({ ...testData, password: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={handleSignUp}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              Sign Up
            </button>
            <button
              onClick={handleSignIn}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Sign In
            </button>
            <button
              onClick={handleSignOut}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="mt-8 text-center space-x-4">
          <a href="/auth/signin" className="text-blue-600 hover:text-blue-800">Sign In Page</a>
          <a href="/auth/signup" className="text-blue-600 hover:text-blue-800">Sign Up Page</a>
          <a href="/dashboard" className="text-blue-600 hover:text-blue-800">Dashboard</a>
          <a href="/" className="text-blue-600 hover:text-blue-800">Home</a>
        </div>
      </div>
    </div>
  );
}
