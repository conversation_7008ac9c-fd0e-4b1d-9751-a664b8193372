import Link from "next/link";

const Navbar = () => {
  return (
    <nav className="w-full bg-white text-black select-none">
      <div className="max-w-6xl mx-auto flex flex-col items-center py-6 px-4">
        <Link href="/" className="text-3xl md:text-4xl font-light tracking-[0.5em] uppercase mb-2 md:mb-4 text-center" style={{letterSpacing: '0.4em'}}>
          OBSESS
        </Link>
        <div className="flex flex-wrap justify-center gap-8 md:gap-12 text-base md:text-lg font-light tracking-widest uppercase">
          <Link href="/products" className="relative pb-1 hover:after:w-full after:content-[''] after:block after:h-[1.5px] after:bg-black after:w-0 after:transition-all after:duration-300">Products</Link>
          <Link href="/cart" className="relative pb-1 hover:after:w-full after:content-[''] after:block after:h-[1.5px] after:bg-black after:w-0 after:transition-all after:duration-300">Cart</Link>
          <Link href="/auth/signin" className="relative pb-1 hover:after:w-full after:content-[''] after:block after:h-[1.5px] after:bg-black after:w-0 after:transition-all after:duration-300">Sign In</Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
