'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Package, Heart, User, Settings, LogOut } from 'lucide-react';
import { signOut } from '@/lib/auth-client';
import { formatPrice, formatDate } from '@/lib/utils';
import AuthGuard from '@/components/auth/AuthGuard';
import { useAuth } from '@/components/auth/AuthProvider';

// Mock data - replace with API calls
const mockOrders = [
  {
    id: "1",
    status: "delivered",
    total: 8998,
    createdAt: "2024-06-20T10:30:00Z",
    items: [
      { name: "Premium Cotton T-Shirt", quantity: 2, price: 2999 },
      { name: "Slim Fit Jeans", quantity: 1, price: 5999 },
    ],
  },
  {
    id: "2",
    status: "shipped",
    total: 12999,
    createdAt: "2024-06-25T14:15:00Z",
    items: [
      { name: "Leather Jacket", quantity: 1, price: 12999 },
    ],
  },
];

const mockWishlist = [
  {
    id: "1",
    name: "Designer Handbag",
    price: 8999,
    compareAtPrice: 11999,
    image: "/images/product-6.jpg",
    slug: "designer-handbag",
  },
  {
    id: "2",
    name: "Casual Sneakers",
    price: 7999,
    image: "/images/product-4.jpg",
    slug: "casual-sneakers",
  },
];

const sidebarItems = [
  { name: 'Overview', href: '/dashboard', icon: User, current: true },
  { name: 'Orders', href: '/dashboard/orders', icon: Package, current: false },
  { name: 'Wishlist', href: '/dashboard/wishlist', icon: Heart, current: false },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings, current: false },
];

export default function DashboardPage() {
  const { user } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'shipped':
        return 'text-blue-600 bg-blue-100';
      case 'confirmed':
        return 'text-yellow-600 bg-yellow-100';
      case 'pending':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <AuthGuard requireAuth={true}>
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{user?.name || 'User'}</h3>
                  <p className="text-sm text-gray-500">{user?.email}</p>
                </div>
              </div>

              <nav className="space-y-2">
                {sidebarItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      item.current
                        ? 'bg-black text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                ))}
                
                <button
                  onClick={handleSignOut}
                  className="flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 w-full text-left"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </button>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Welcome Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome back, {user?.name?.split(' ')[0] || 'User'}!
              </h1>
              <p className="text-gray-600">
                Here's what's happening with your account today.
              </p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Orders</p>
                    <p className="text-2xl font-bold text-gray-900">{mockOrders.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <Heart className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Wishlist Items</p>
                    <p className="text-2xl font-bold text-gray-900">{mockWishlist.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-bold">₹</span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Spent</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatPrice(mockOrders.reduce((sum, order) => sum + order.total, 0))}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Orders */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Recent Orders</h2>
                <Link
                  href="/dashboard/orders"
                  className="text-black hover:text-gray-600 font-medium text-sm"
                >
                  View All
                </Link>
              </div>

              <div className="space-y-4">
                {mockOrders.slice(0, 3).map((order) => (
                  <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <p className="font-medium text-gray-900">Order #{order.id}</p>
                        <p className="text-sm text-gray-500">{formatDate(order.createdAt)}</p>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                        <p className="text-sm font-medium text-gray-900 mt-1">
                          {formatPrice(order.total)}
                        </p>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      {order.items.map((item, index) => (
                        <span key={index}>
                          {item.name} (x{item.quantity})
                          {index < order.items.length - 1 && ', '}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Wishlist Preview */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Wishlist</h2>
                <Link
                  href="/dashboard/wishlist"
                  className="text-black hover:text-gray-600 font-medium text-sm"
                >
                  View All
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockWishlist.slice(0, 2).map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <Link
                        href={`/products/${item.slug}`}
                        className="font-medium text-gray-900 hover:text-gray-600"
                      >
                        {item.name}
                      </Link>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-sm font-semibold text-gray-900">
                          {formatPrice(item.price)}
                        </span>
                        {item.compareAtPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(item.compareAtPrice)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </AuthGuard>
  );
}
